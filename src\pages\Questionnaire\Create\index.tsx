import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { history, useModel, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Radio,
  Row,
  Space,
  Spin,
  Switch,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import './index.less';

// 定义星级模式常量
const StarMode = {
  FIVE_STAR: 5,
  TEN_STAR: 10,
} as const;

const { TextArea } = Input;

/**
 * 问卷创建/编辑页面
 */
const QuestionnaireCreate: React.FC = () => {
  const {
    questionnaireDetail,
    detailLoading,
    createQuestionnaireAction,
    updateQuestionnaireAction,
    fetchQuestionnaireDetail,
  } = useModel('questionnaire');

  const { initialState } = useModel('@@initialState');

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const params = useParams<{ id: string }>();
  const isEdit = !!params.id;

  // 初始化数据
  useEffect(() => {
    if (isEdit && params.id) {
      fetchQuestionnaireDetail(Number(params.id));
    }
  }, [isEdit, params.id]);

  // 编辑时填充表单数据
  useEffect(() => {
    if (isEdit && questionnaireDetail) {
      const formValues = {
        title: questionnaireDetail.title,
        description: questionnaireDetail.description,
        month: dayjs(questionnaireDetail.month),
        star_mode: questionnaireDetail.star_mode || StarMode.FIVE_STAR,
        include_school_evaluation:
          questionnaireDetail.include_school_evaluation,
        instructions: questionnaireDetail.instructions,
        allow_anonymous: questionnaireDetail.allow_anonymous ?? false,
        max_teachers_limit: questionnaireDetail.max_teachers_limit ?? 0,
        start_time: questionnaireDetail.start_time
          ? dayjs(questionnaireDetail.start_time)
          : undefined,
        end_time: questionnaireDetail.end_time
          ? dayjs(questionnaireDetail.end_time)
          : undefined,
      };

      form.setFieldsValue(formValues);
    }
  }, [isEdit, questionnaireDetail, form]);

  // 表单提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 格式化数据
      const formattedValues: API.ICreateQuestionnaireParams = {
        title: values.title,
        description: values.description,
        month: values.month.format('YYYY-MM'),
        sso_school_code: initialState?.enterprise?.code || '',
        star_mode: values.star_mode,
        include_school_evaluation: values.include_school_evaluation,
        instructions: values.instructions,
        allow_anonymous: values.allow_anonymous,
        max_teachers_limit: values.max_teachers_limit,
        start_time: values.start_time ? values.start_time.toDate() : undefined,
        end_time: values.end_time ? values.end_time.toDate() : undefined,
      };

      let result;
      if (isEdit && params.id) {
        result = await updateQuestionnaireAction(
          Number(params.id),
          formattedValues,
        );
      } else {
        result = await createQuestionnaireAction(formattedValues);
      }

      if (result.success) {
        // 跳转到问卷列表页
        history.push('/questionnaire/list');
      }
    } catch (error: any) {
      // 显示具体的错误信息，如果没有则显示默认消息
      const errorMessage =
        error?.message || (isEdit ? '更新问卷失败' : '创建问卷失败');
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    history.push('/questionnaire/list');
  };

  return (
    <div className="questionnaire-create">
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 16 }}
            >
              返回列表
            </Button>
            <h2 style={{ margin: 0 }}>{isEdit ? '编辑问卷' : '创建问卷'}</h2>
          </div>
        </div>
      </Card>

      {/* 表单内容 */}
      <Card>
        <Spin spinning={detailLoading}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              star_mode: StarMode.FIVE_STAR, // 默认5星制
              include_school_evaluation: true, // 默认包含学校评价
              allow_anonymous: false, // 默认不允许匿名
              max_teachers_limit: 0, // 默认无限制
            }}
          >
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="title"
                  label="问卷标题"
                  rules={[
                    { required: true, message: '问卷标题不能为空' },
                    { max: 200, message: '问卷标题不能超过200个字符' },
                  ]}
                >
                  <Input
                    placeholder="请输入问卷标题"
                    maxLength={200}
                    showCount
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="所属学校">
                  <Input
                    value={initialState?.enterprise?.name || ''}
                    disabled
                    placeholder="当前用户学校"
                  />
                  <div className="form-description">
                    问卷将创建在您所属的学校下
                  </div>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="month"
                  label="问卷月份"
                  rules={[{ required: true, message: '请选择问卷月份' }]}
                >
                  <DatePicker.MonthPicker
                    placeholder="请选择月份"
                    format="YYYY-MM"
                    style={{ width: '100%' }}
                    disabledDate={(current) => {
                      // 禁用未来月份
                      return current && current > dayjs().endOf('month');
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="star_mode"
                  label="星级模式"
                  rules={[{ required: true, message: '请选择星级模式' }]}
                >
                  <Radio.Group>
                    <Radio value={StarMode.FIVE_STAR}>5星制</Radio>
                    <Radio value={StarMode.TEN_STAR}>10星制</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={24}>
                <Form.Item
                  name="description"
                  label="问卷描述"
                  rules={[{ max: 1000, message: '问卷描述不能超过1000个字符' }]}
                >
                  <TextArea
                    placeholder="请输入问卷描述（可选）"
                    rows={4}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 问卷说明 */}
            {/* <Row>
              <Col span={24}>
                <Form.Item
                  name="instructions"
                  label="问卷说明"
                  rules={[{ max: 1000, message: '问卷说明不能超过1000个字符' }]}
                >
                  <TextArea
                    placeholder="请输入问卷说明（可选）"
                    rows={3}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>
              </Col>
            </Row> */}

            {/* 高级设置 */}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="include_school_evaluation"
                  label="包含学校评价"
                  valuePropName="checked"
                  getValueFromEvent={(checked) => {
                    console.log('getValueFromEvent:', checked);
                    return checked;
                  }}
                >
                  <Switch />
                  <div className="form-description">
                    是否在问卷中包含对学校的整体评价
                  </div>
                </Form.Item>
              </Col>
              {/* <Col span={12}>
                <Form.Item
                  name="allow_anonymous"
                  label="允许匿名评价"
                  valuePropName="checked"
                >
                  <Switch />
                  <div className="form-description">
                    是否允许学长匿名提交评价
                  </div>
                </Form.Item>
              </Col> */}
            </Row>

            {/* <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="max_teachers_limit"
                  label="最大评价教师数量"
                  rules={[
                    { type: 'number', min: 0, message: '最大评价教师数量不能小于0' },
                  ]}
                >
                  <InputNumber
                    placeholder="0表示无限制"
                    min={0}
                    style={{ width: '100%' }}
                  />
                  <div className="form-description">
                    限制每个学生最多可以评价的教师数量，0表示无限制
                  </div>
                </Form.Item>
              </Col>
            </Row> */}

            {/* 时间设置 */}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item name="start_time" label="问卷开始时间">
                  <DatePicker
                    showTime
                    placeholder="请选择开始时间（可选）"
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                  <div className="form-description">不设置则立即生效</div>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="end_time" label="问卷结束时间">
                  <DatePicker
                    showTime
                    placeholder="请选择结束时间（可选）"
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                  <div className="form-description">不设置则永不过期</div>
                </Form.Item>
              </Col>
            </Row>

            {/* 提交按钮 */}
            <Form.Item>
              <div style={{ textAlign: 'center', marginTop: 32 }}>
                <Space size="large">
                  <Button size="large" onClick={handleBack}>
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    htmlType="submit"
                    loading={loading}
                    icon={<SaveOutlined />}
                  >
                    {isEdit ? '保存修改' : '创建问卷'}
                  </Button>
                </Space>
              </div>
            </Form.Item>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default QuestionnaireCreate;
